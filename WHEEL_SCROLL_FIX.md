# WheelPicker 鼠标滚轮修复

## 问题描述

在JS和WASM平台上，WheelPicker组件存在鼠标滚轮滚动时可能一次滚动多个item的问题。这是由于：

1. 不同平台的鼠标滚轮事件敏感度不同
2. JS/WASM平台的滚轮事件可能在短时间内触发多次
3. 原始的LazyColumn直接响应所有滚轮事件，没有进行防抖处理

## 解决方案

### 1. 平台特定的滚轮处理

创建了一个平台特定的滚轮事件处理系统：

- **Common**: 定义了`WheelScrollHandler`接口
- **JS/WASM**: 实现了带防抖功能的滚轮处理器
- **其他平台**: 使用默认的滚轮处理（无需特殊处理）

### 2. 防抖机制

在JS和WASM平台实现了以下防抖策略：

- **时间防抖**: 150ms内的重复滚轮事件被忽略
- **状态防抖**: 正在滚动时忽略新的滚轮事件
- **阈值过滤**: 忽略过小的滚轮增量（< 0.1f）

### 3. 精确滚动控制

- 根据滚轮方向和当前偏移量计算目标索引
- 确保每次滚轮操作只移动一个item
- 使用动画滚动提供流畅的用户体验

## 实现细节

### 文件结构

```
datetime-wheel-picker/src/
├── commonMain/kotlin/.../WheelScrollHandler.kt     # 接口定义
├── jsMain/kotlin/.../WheelScrollHandler.kt         # JS平台实现
├── wasmJsMain/kotlin/.../WheelScrollHandler.kt     # WASM平台实现
└── nonJsMain/kotlin/.../WheelScrollHandler.kt      # 其他平台实现
```

### 核心代码

#### 接口定义 (Common)
```kotlin
interface WheelScrollHandler {
    @Composable
    fun createScrollModifier(
        lazyListState: LazyListState,
        itemHeight: Float
    ): Modifier
}

expect fun getWheelScrollHandler(): WheelScrollHandler
```

#### JS/WASM实现要点
```kotlin
@OptIn(ExperimentalComposeUiApi::class)
@Composable
override fun createScrollModifier(
    lazyListState: LazyListState,
    itemHeight: Float
): Modifier {
    var lastScrollTime by remember { mutableLongStateOf(0L) }
    var isScrolling by remember { mutableStateOf(false) }
    
    return Modifier.onPointerEvent(PointerEventType.Scroll) { pointerEvent ->
        // 防抖逻辑
        // 计算目标索引
        // 执行动画滚动
    }
}
```

## 使用方法

修复后的WheelPicker会自动在JS和WASM平台使用新的滚轮处理逻辑，其他平台保持原有行为。无需修改现有的API调用。

## 测试

可以通过以下方式测试修复效果：

1. 构建JS版本：`./gradlew :sample:composeApp:jsBrowserDevelopmentRun`
2. 构建WASM版本：`./gradlew :sample:composeApp:wasmJsBrowserDevelopmentRun`
3. 在浏览器中测试鼠标滚轮行为

## 兼容性

- ✅ Android: 无影响，使用默认处理
- ✅ iOS: 无影响，使用默认处理  
- ✅ JVM Desktop: 无影响，使用默认处理
- ✅ JS: 使用新的防抖滚轮处理
- ✅ WASM: 使用新的防抖滚轮处理
