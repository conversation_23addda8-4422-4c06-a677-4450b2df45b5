# WheelPicker 鼠标滚轮修复

## 问题描述

在JS和WASM平台上，WheelPicker组件存在鼠标滚轮滚动时可能一次滚动多个item的问题。这是由于：

1. 不同平台的鼠标滚轮事件敏感度不同
2. JS/WASM平台的滚轮事件可能在短时间内触发多次
3. 原始的LazyColumn直接响应所有滚轮事件，没有进行防抖处理

## 解决方案

### 1. 平台特定的滚轮处理

创建了一个平台特定的滚轮事件处理系统：

- **Common**: 定义了`WheelScrollHandler`接口
- **JS/WASM**: 实现了带防抖功能的滚轮处理器
- **其他平台**: 使用默认的滚轮处理（无需特殊处理）

### 2. 防抖机制

在JS和WASM平台实现了以下防抖策略：

- **时间防抖**: 150ms内的重复滚轮事件被忽略
- **状态防抖**: 正在滚动时忽略新的滚轮事件
- **阈值过滤**: 忽略过小的滚轮增量（< 0.1f）

### 3. 精确滚动控制

- 根据滚轮方向和当前偏移量计算目标索引
- 确保每次滚轮操作只移动一个item
- 使用动画滚动提供流畅的用户体验

## 实现细节

### 文件结构

```
datetime-wheel-picker/src/
├── commonMain/kotlin/.../WheelScrollHandler.kt     # 接口定义
├── jsMain/kotlin/.../WheelScrollHandler.kt         # JS平台实现
├── wasmJsMain/kotlin/.../WheelScrollHandler.kt     # WASM平台实现
└── nonJsMain/kotlin/.../WheelScrollHandler.kt      # 其他平台实现
```

### 核心代码

#### 接口定义 (Common)
```kotlin
interface WheelScrollHandler {
    @Composable
    fun createScrollModifier(
        lazyListState: LazyListState,
        itemHeight: Float
    ): Modifier
}

expect fun getWheelScrollHandler(): WheelScrollHandler
```

#### JS/WASM实现要点
```kotlin
@OptIn(ExperimentalComposeUiApi::class)
@Composable
override fun createScrollModifier(
    lazyListState: LazyListState,
    itemHeight: Float
): Modifier {
    var lastScrollTime by remember { mutableLongStateOf(0L) }
    var isScrolling by remember { mutableStateOf(false) }

    return Modifier.onPointerEvent(PointerEventType.Scroll) { pointerEvent ->
        // 消费事件以阻止默认滚动行为
        pointerEvent.changes.forEach { it.consume() }

        // 强防抖逻辑 (500ms)
        if (currentTime - lastScrollTime < 500 || isScrolling) {
            return@onPointerEvent
        }

        // 简化的滚动逻辑：每次只移动一个item
        val targetIndex = when (scrollDirection) {
            1 -> (currentIndex + 1).coerceAtMost(totalItems - 1)
            -1 -> (currentIndex - 1).coerceAtLeast(0)
            else -> currentIndex
        }

        // 使用scrollToItem进行即时定位
        lazyListState.scrollToItem(targetIndex)
    }
}
```

### 关键修复点

1. **事件消费**: `pointerEvent.changes.forEach { it.consume() }` - 完全阻止默认滚动
2. **强防抖**: 500ms防抖时间，确保不会快速连续滚动
3. **简化逻辑**: 移除复杂的偏移计算，每次滚动固定移动一个item
4. **即时定位**: 使用`scrollToItem`而不是`animateScrollToItem`，避免动画重叠

## 使用方法

修复后的WheelPicker会自动在JS和WASM平台使用新的滚轮处理逻辑，其他平台保持原有行为。无需修改现有的API调用。

## 测试

可以通过以下方式测试修复效果：

1. **启动JS开发服务器**：
   ```bash
   ./gradlew :sample:composeApp:jsBrowserDevelopmentRun --continuous
   ```
   然后访问 http://localhost:8080/

2. **启动WASM开发服务器**：
   ```bash
   ./gradlew :sample:composeApp:wasmJsBrowserDevelopmentRun --continuous
   ```

3. **测试行为**：
   - 在浏览器中打开应用
   - 使用鼠标滚轮在WheelPicker组件上滚动
   - 验证每次滚轮操作只移动一个item
   - 验证快速滚动时不会跳过多个item

### 预期行为

- ✅ 每次鼠标滚轮操作只移动一个item
- ✅ 快速滚动时有防抖保护
- ✅ 滚动方向正确（向上/向下）
- ✅ 不会出现滚动"跳跃"现象

## 兼容性

- ✅ Android: 无影响，使用默认处理
- ✅ iOS: 无影响，使用默认处理  
- ✅ JVM Desktop: 无影响，使用默认处理
- ✅ JS: 使用新的防抖滚轮处理
- ✅ WASM: 使用新的防抖滚轮处理
