package dev.darkokoa.datetimewheelpicker.core

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEvent

/**
 * Interface for handling wheel scroll events in a platform-specific manner.
 * This allows different platforms to implement custom scroll behavior,
 * particularly for JS/WASM platforms that may have different scroll sensitivities.
 */
interface WheelScrollHandler {
    /**
     * Creates a modifier that handles wheel scroll events for the WheelPicker.
     * 
     * @param lazyListState The LazyListState to control scrolling
     * @param itemHeight The height of each item in the wheel picker
     * @return A Modifier that handles scroll events appropriately for the platform
     */
    @Composable
    fun createScrollModifier(
        lazyListState: LazyListState,
        itemHeight: Float
    ): Modifier
}

/**
 * Expect declaration for getting the platform-specific wheel scroll handler.
 */
expect fun getWheelScrollHandler(): WheelScrollHandler
