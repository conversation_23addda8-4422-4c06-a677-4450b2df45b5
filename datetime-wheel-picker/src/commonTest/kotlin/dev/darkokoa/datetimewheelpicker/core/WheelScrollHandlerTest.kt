package dev.darkokoa.datetimewheelpicker.core

import kotlin.test.Test
import kotlin.test.assertNotNull

/**
 * Test for WheelScrollHandler to ensure platform-specific implementations are available.
 */
class WheelScrollHandlerTest {
    
    @Test
    fun testWheelScrollHandlerIsAvailable() {
        // Test that we can get a wheel scroll handler instance
        val handler = getWheelScrollHandler()
        assertNotNull(handler, "WheelScrollHandler should not be null")
    }
    
    @Test
    fun testWheelScrollHandlerImplementsInterface() {
        // Test that the returned handler implements the correct interface
        val handler = getWheelScrollHandler()
        kotlin.test.assertTrue(handler is WheelScrollHandler,
            "Hand<PERSON> should implement WheelScrollHandler interface")
    }
}
