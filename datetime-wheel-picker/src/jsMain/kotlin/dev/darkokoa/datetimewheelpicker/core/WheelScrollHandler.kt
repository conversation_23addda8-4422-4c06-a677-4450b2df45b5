package dev.darkokoa.datetimewheelpicker.core

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.onPointerEvent
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlin.math.abs
import kotlin.math.sign

/**
 * JS-specific implementation that handles mouse wheel scroll events
 * with strong debouncing to prevent multiple items from scrolling at once.
 */
internal class JsWheelScrollHandler : WheelScrollHandler {

    @OptIn(ExperimentalComposeUiApi::class)
    @Composable
    override fun createScrollModifier(
        lazyListState: LazyListState,
        itemHeight: Float
    ): Modifier {
        // State to track the last scroll time for debouncing
        var lastScrollTime by remember { mutableLongStateOf(0L) }
        var isScrolling by remember { mutableStateOf(false) }

        return Modifier.onPointerEvent(PointerEventType.Scroll) { pointerEvent ->
            val currentTime = Clock.System.now().toEpochMilliseconds()
            val scrollDelta = pointerEvent.changes.firstOrNull()?.scrollDelta?.y ?: 0f

            // Consume the event to prevent default scrolling behavior
            pointerEvent.changes.forEach { it.consume() }

            // Only process significant scroll deltas
            if (abs(scrollDelta) < 0.5f) return@onPointerEvent

            // Very strong debounce: ignore rapid scroll events (increased to 500ms)
            if (currentTime - lastScrollTime < 500 || isScrolling) {
                return@onPointerEvent
            }

            lastScrollTime = currentTime
            isScrolling = true

            // Calculate target scroll position
            val currentIndex = lazyListState.firstVisibleItemIndex
            val totalItems = lazyListState.layoutInfo.totalItemsCount

            // Determine scroll direction - simplified logic
            val scrollDirection = if (scrollDelta > 0) 1 else -1

            // Calculate target index with simpler logic
            val targetIndex = when (scrollDirection) {
                1 -> {
                    // Scrolling down - always go to next item
                    (currentIndex + 1).coerceAtMost(totalItems - 1)
                }
                -1 -> {
                    // Scrolling up - always go to previous item
                    (currentIndex - 1).coerceAtLeast(0)
                }
                else -> currentIndex
            }

            // Only scroll if target is different from current
            if (targetIndex != currentIndex) {
                GlobalScope.launch {
                    try {
                        // Use scrollToItem for immediate positioning instead of animation
                        lazyListState.scrollToItem(targetIndex)
                        delay(300) // Even longer delay to prevent rapid scrolling
                    } finally {
                        isScrolling = false
                    }
                }
            } else {
                isScrolling = false
            }
        }
    }
}

/**
 * Actual implementation for JS platform.
 */
actual fun getWheelScrollHandler(): WheelScrollHandler = JsWheelScrollHandler()
