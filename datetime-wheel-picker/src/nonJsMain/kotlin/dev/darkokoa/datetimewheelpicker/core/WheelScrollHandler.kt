package dev.darkokoa.datetimewheelpicker.core

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

/**
 * Default implementation for non-JS platforms.
 * These platforms typically have proper scroll handling built-in.
 */
internal class DefaultWheelScrollHandler : WheelScrollHandler {
    @Composable
    override fun createScrollModifier(
        lazyListState: LazyListState,
        itemHeight: Float
    ): Modifier {
        // For non-JS platforms, we don't need custom scroll handling
        // The default LazyColumn scroll behavior works fine
        return Modifier
    }
}

/**
 * Actual implementation for non-JS platforms.
 */
actual fun getWheelScrollHandler(): WheelScrollHandler = DefaultWheelScrollHandler()
