package dev.darkokoa.datetimewheelpicker.core

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.onPointerEvent
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlin.math.abs
import kotlin.math.sign

/**
 * WASM-specific implementation that handles mouse wheel scroll events
 * with debouncing to prevent multiple items from scrolling at once.
 * Similar to JS implementation but optimized for WASM runtime.
 */
internal class WasmWheelScrollHandler : WheelScrollHandler {
    
    @OptIn(ExperimentalComposeUiApi::class)
    @Composable
    override fun createScrollModifier(
        lazyListState: LazyListState,
        itemHeight: Float
    ): Modifier {
        // State to track the last scroll time for debouncing
        var lastScrollTime by remember { mutableLongStateOf(0L) }
        var isScrolling by remember { mutableStateOf(false) }
        
        return Modifier.onPointerEvent(PointerEventType.Scroll) { pointerEvent ->
            val currentTime = Clock.System.now().toEpochMilliseconds()
            val scrollDelta = pointerEvent.changes.firstOrNull()?.scrollDelta?.y ?: 0f
            
            // Only process significant scroll deltas
            if (abs(scrollDelta) < 0.1f) return@onPointerEvent
            
            // Debounce: ignore rapid scroll events
            if (currentTime - lastScrollTime < 150 || isScrolling) {
                return@onPointerEvent
            }
            
            lastScrollTime = currentTime
            isScrolling = true
            
            // Calculate target scroll position immediately
            val currentIndex = lazyListState.firstVisibleItemIndex
            val currentOffset = lazyListState.firstVisibleItemScrollOffset

            // Determine scroll direction and calculate target index
            val scrollDirection = sign(scrollDelta).toInt()
            val targetIndex = when {
                scrollDirection > 0 -> {
                    // Scrolling down
                    if (currentOffset > itemHeight / 2) {
                        currentIndex + 1
                    } else {
                        currentIndex
                    }
                }
                scrollDirection < 0 -> {
                    // Scrolling up
                    if (currentOffset < itemHeight / 2) {
                        (currentIndex - 1).coerceAtLeast(0)
                    } else {
                        currentIndex
                    }
                }
                else -> currentIndex
            }

            // Ensure target index is within bounds
            val finalTargetIndex = targetIndex.coerceIn(
                0,
                lazyListState.layoutInfo.totalItemsCount - 1
            )

            // Only scroll if target is different from current
            if (finalTargetIndex != currentIndex || currentOffset != 0) {
                // Use a simple scroll to item instead of animation to avoid composable context issues
                GlobalScope.launch {
                    try {
                        lazyListState.animateScrollToItem(finalTargetIndex)
                        delay(100)
                    } finally {
                        isScrolling = false
                    }
                }
            } else {
                isScrolling = false
            }
        }
    }
}

/**
 * Actual implementation for WASM platform.
 */
actual fun getWheelScrollHandler(): WheelScrollHandler = WasmWheelScrollHandler()
